const request = require('supertest');
const express = require('express');
const errorHandler = require('../../src/middlewares/errorHandler');

// Mock fetch globally
global.fetch = jest.fn();

describe('Error Notification Integration', () => {
  let testApp;

  beforeEach(() => {
    fetch.mockReset();
    jest.spyOn(console, 'error').mockImplementation();

    // Create a test app with error handler
    testApp = express();
    testApp.use(express.json());
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should send notification when server error occurs', async () => {
    // Mock successful webhook response
    fetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
    });

    // Add test route that throws an error
    testApp.get('/test-error', (req, res, next) => {
      const error = new Error('Test server error for notification');
      next(error);
    });

    // Add error handler
    testApp.use(errorHandler);

    // Make request that will trigger an error
    const response = await request(testApp).get('/test-error').expect(500);

    // Verify error response
    expect(response.body).toEqual({
      error: 'Internal Server Error',
      details: [],
      backtrace: expect.any(Array),
    });

    // Wait for async notification to complete
    await new Promise(resolve => setTimeout(resolve, 100));

    // Verify notification was sent (if webhook URL is configured)
    if (process.env.MM_ERROR_WEBHOOK_URL) {
      expect(fetch).toHaveBeenCalledWith(
        process.env.MM_ERROR_WEBHOOK_URL,
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: expect.stringContaining('Test server error for notification'),
        }),
      );
    } else {
      // If no webhook URL configured, should log warning
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('MM_ERROR_WEBHOOK_URL not configured'),
      );
    }
  });

  it('should not send notification for client errors (4xx)', async () => {
    // Add 404 handler
    testApp.use((req, res) => {
      res.status(404).json({ error: 'Not Found' });
    });

    // Make request to non-existent route (404 error)
    await request(testApp).get('/non-existent-route').expect(404);

    // Wait for any potential async operations
    await new Promise(resolve => setTimeout(resolve, 100));

    // Verify no notification was sent
    expect(fetch).not.toHaveBeenCalled();
  });

  it('should handle notification failures gracefully', async () => {
    // Mock webhook failure
    fetch.mockRejectedValueOnce(new Error('Webhook failed'));

    // Add test route that throws an error
    testApp.get('/test-error-with-webhook-failure', (req, res, next) => {
      const error = new Error('Test error with webhook failure');
      next(error);
    });

    // Add error handler
    testApp.use(errorHandler);

    // Make request that will trigger an error
    const response = await request(testApp).get('/test-error-with-webhook-failure').expect(500);

    // Verify error response is still returned normally
    expect(response.body).toEqual({
      error: 'Internal Server Error',
      details: [],
      backtrace: expect.any(Array),
    });

    // Wait for async notification to complete
    await new Promise(resolve => setTimeout(resolve, 100));

    // Verify notification failure was logged
    if (process.env.MM_ERROR_WEBHOOK_URL) {
      expect(console.error).toHaveBeenCalledWith(
        'Failed to send error notification:',
        expect.any(Error),
      );
    }
  });
});
