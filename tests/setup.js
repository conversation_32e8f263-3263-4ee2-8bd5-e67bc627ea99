require('dotenv').config({ path: '.env.test', quiet: true });
const { sequelize } = require('../src/models');

const {
  beforeAllBase,
  beforeEachBase,
  afterEachBase,
  afterAllBase,
} = require('./utils/dbCleanerHooks');

// Set test environment
process.env.NODE_ENV = 'test';

const QdrantServiceMock = require('./mocks/QdrantServiceMock');
const GoogleAiServiceMock = require('./mocks/GoogleAiServiceMock');

// Mock Qdrant client globally to avoid connection failures
jest.mock('@qdrant/js-client-rest', () => {
  return {
    QdrantClient: jest.fn().mockImplementation(() => new QdrantServiceMock()),
  };
});

// Mock Google AI service globally to avoid expensive API calls
jest.mock('@google/genai', () => {
  return {
    GoogleGenAI: jest.fn().mockImplementation(() => new GoogleAiServiceMock()),
  };
});

// Mock VacancyGroupVariableService to avoid complex SQL operations during tests
jest.mock('../src/services/VacancyGroupVariableService', () => {
  const originalModule = jest.requireActual('../src/services/VacancyGroupVariableService');

  return class VacancyGroupVariableService extends originalModule {
    async calculateUserVacancyVariableScores(_jobVacancyId) {
      // Mock implementation that doesn't execute complex SQL
      return Promise.resolve();
    }
  };
});

// Mock OnetService to avoid database queries to non-existent onet_occupations table
jest.mock('../src/services/OnetService', () => {
  return class OnetService {
    async getOccupations(_onetsocCodes) {
      // Mock implementation that returns sample occupation data
      return [
        {
          onetsoc_code: '15-1252.00',
          title: 'Software Developers, Applications',
          description:
            'Develop, create, and modify general computer applications software or specialized utility programs.',
        },
        {
          onetsoc_code: '15-1251.00',
          title: 'Computer Programmers',
          description:
            'Create, modify, and test the code and scripts that allow computer applications to run.',
        },
      ];
    }

    async getTasks(_onetsocCodes) {
      // Mock implementation that returns sample task data
      return [
        {
          onetsoc_code: '15-1252.00',
          task: 'Analyze user requirements to derive technical software design and performance requirements.',
        },
        {
          onetsoc_code: '15-1252.00',
          task: 'Debug, maintain, and update existing software applications.',
        },
        {
          onetsoc_code: '15-1251.00',
          task: 'Write, update, and maintain computer programs or software packages.',
        },
      ];
    }
  };
});

// Mock job description generation service to avoid external API calls
jest.mock('../src/services/job_vacancy/GenerateJobDescService', () => {
  const originalModule = jest.requireActual('../src/services/job_vacancy/GenerateJobDescService');

  return class GenerateJobDescService extends originalModule {
    async generateJobDesc(_name, _topUserIds, _jobLevelName) {
      return {
        jobTitle: 'Software Engineer',
        jobDescription: {
          key_responsibilities: ['Develop and maintain software applications'],
          qualifications: ["Bachelor's degree in Computer Science"],
          competencies: ['JavaScript', 'Node.js'],
          success_metrics: ['Deliver projects on time'],
        },
        onetsocCodes: ['15-1132.00'],
      };
    }
  };
});

// Mock job vacancy service to avoid external API calls
jest.mock('../src/services/JobVacancyService', () => {
  const originalModule = jest.requireActual('../src/services/JobVacancyService');

  return class JobVacancyService extends originalModule {
    async setJobDesc(_data) {
      return;
    }

    async setVacancyGroupVariables(_data) {
      return;
    }
  };
});

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to suppress console.log in tests
  // log: jest.fn(),
  // info: jest.fn(),
  // warn: jest.fn(),
  // error: jest.fn(),
};

// Global test timeout
jest.setTimeout(3000);

beforeEach(async () => {
  await beforeEachBase();
});

afterEach(async () => {
  await afterEachBase();
  jest.clearAllMocks();
});

beforeAll(async () => {
  await beforeAllBase();
});

afterAll(async () => {
  await afterAllBase();

  try {
    await sequelize.close();
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
});
