// Reference: https://github.com/sequelize/sequelize/issues/11408#issuecomment-1102321495
const { sequelize, namespace } = require('../../src/models');

let transaction;
let wrapperTransaction;

const beforeAllBase = async () => {
  transaction = await sequelize?.transaction({
    autocommit: false,
  });

  const context = namespace.createContext();
  namespace.enter(context);
  namespace.set('transaction', transaction);
};

const beforeEachBase = async () => {
  wrapperTransaction = await sequelize?.transaction({
    autocommit: false,
    transaction,
  });

  namespace.set('transaction', wrapperTransaction);
};

const afterEachBase = async () => {
  await wrapperTransaction?.rollback();
};

const afterAllBase = async () => {
  await transaction?.rollback();
};

module.exports = {
  beforeAllBase,
  beforeEachBase,
  afterEachBase,
  afterAllBase,
};
