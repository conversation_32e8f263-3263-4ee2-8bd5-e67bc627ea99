const api = require('../utils/requestHelper');

const { UserFactory, BoneFactory } = require('../factories');
const { Bone } = require('../../src/models');

describe('BonesController', () => {
  let admin;
  let user;

  beforeEach(async () => {
    admin = await UserFactory.create({ role: 'admin' });
    user = await UserFactory.create();

    // Create test bones
    await BoneFactory.create({ name: 'Technical' });
    await BoneFactory.create({ name: 'Leadership' });
    await BoneFactory.create({ name: 'Communication' });
  });

  describe('GET /api/v1/bones', () => {
    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await api.get('/api/v1/bones');

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await api.as(user).get('/api/v1/bones');

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });
    });

    describe('validation', () => {
      it('should return 400 when sort field is invalid', async () => {
        const response = await api.as(admin).get('/api/v1/bones', {
          sort: 'invalid_field',
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 when sort_direction is invalid', async () => {
        const response = await api.as(admin).get('/api/v1/bones', {
          sort_direction: 'invalid',
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 when page is less than 1', async () => {
        const response = await api.as(admin).get('/api/v1/bones', {
          page: 0,
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 when limit exceeds maximum', async () => {
        const response = await api.as(admin).get('/api/v1/bones', {
          limit: 101,
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });
    });

    describe('successful responses', () => {
      it('should return bones with correct format', async () => {
        const response = await api.as(admin).get('/api/v1/bones');

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('pagination');
        expect(Array.isArray(response.body.data)).toBe(true);
        expect(response.body.data.length).toBeGreaterThanOrEqual(3);

        const bone = response.body.data[0];
        expect(bone).toHaveProperty('id');
        expect(bone).toHaveProperty('name');
        expect(typeof bone.id).toBe('number');
        expect(typeof bone.name).toBe('string');
      });

      it('should support pagination', async () => {
        const response = await api.as(admin).get('/api/v1/bones', {
          page: 1,
          limit: 2,
        });

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(2);
        expect(response.body.pagination).toMatchObject({
          page: 1,
          limit: 2,
          total: expect.any(Number),
        });
      });

      it('should support sorting by name ascending', async () => {
        const response = await api.as(admin).get('/api/v1/bones', {
          sort: 'name',
          sort_direction: 'asc',
        });

        expect(response.status).toBe(200);
        expect(response.body.data.length).toBeGreaterThanOrEqual(3);

        // Check if sorted alphabetically
        const names = response.body.data.map(bone => bone.name);
        const sortedNames = [...names].sort();
        expect(names).toEqual(sortedNames);
      });

      it('should support sorting by name descending', async () => {
        const response = await api.as(admin).get('/api/v1/bones', {
          sort: 'name',
          sort_direction: 'desc',
        });

        expect(response.status).toBe(200);
        expect(response.body.data.length).toBeGreaterThanOrEqual(3);

        // Check if sorted reverse alphabetically
        const names = response.body.data.map(bone => bone.name);
        const sortedNames = [...names].sort().reverse();
        expect(names).toEqual(sortedNames);
      });

      it('should support sorting by id', async () => {
        const response = await api.as(admin).get('/api/v1/bones', {
          sort: 'id',
          sort_direction: 'asc',
        });

        expect(response.status).toBe(200);
        expect(response.body.data.length).toBeGreaterThanOrEqual(3);

        // Check if sorted by id
        const ids = response.body.data.map(bone => bone.id);
        const sortedIds = [...ids].sort((a, b) => a - b);
        expect(ids).toEqual(sortedIds);
      });

      it('should support sorting by created_at', async () => {
        const response = await api.as(admin).get('/api/v1/bones', {
          sort: 'created_at',
          sort_direction: 'desc',
        });

        expect(response.status).toBe(200);
        expect(response.body.data.length).toBeGreaterThanOrEqual(3);
      });

      it('should support sorting by updated_at', async () => {
        const response = await api.as(admin).get('/api/v1/bones', {
          sort: 'updated_at',
          sort_direction: 'asc',
        });

        expect(response.status).toBe(200);
        expect(response.body.data.length).toBeGreaterThanOrEqual(3);
      });
    });

    describe('edge cases', () => {
      it('should handle empty results gracefully', async () => {
        // Delete all bones
        await Bone.destroy({ where: {}, force: true });

        const response = await api.as(admin).get('/api/v1/bones');

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
        expect(response.body.pagination.total).toBe(0);
      });

      it('should handle large page numbers', async () => {
        const response = await api.as(admin).get('/api/v1/bones', {
          page: 999,
          limit: 10,
        });

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
        expect(response.body.pagination.page).toBe(999);
      });

      it('should use default sorting when no sort specified', async () => {
        const response = await api.as(admin).get('/api/v1/bones');

        expect(response.status).toBe(200);
        expect(response.body.data.length).toBeGreaterThanOrEqual(3);

        // Default sort should be by name ascending
        const names = response.body.data.map(bone => bone.name);
        const sortedNames = [...names].sort();
        expect(names).toEqual(sortedNames);
      });

      it('should handle search parameter gracefully', async () => {
        // Even though search isn't implemented in the repository,
        // it should be accepted by validation
        const response = await api.as(admin).get('/api/v1/bones', {
          search: 'Technical',
        });

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
      });

      it('should coerce string numbers to integers for pagination', async () => {
        const response = await api.as(admin).get('/api/v1/bones', {
          page: '2',
          limit: '1',
        });

        expect(response.status).toBe(200);
        expect(response.body.pagination.page).toBe(2);
        expect(response.body.pagination.limit).toBe(1);
      });
    });
  });
});
