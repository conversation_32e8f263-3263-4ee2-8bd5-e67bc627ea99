const api = require('../utils/requestHelper');

const { UserFactory, JobLevelFactory } = require('../factories');
const { JobLevel } = require('../../src/models');

describe('JobLevelsController', () => {
  let admin;
  let user;

  beforeEach(async () => {
    admin = await UserFactory.create({ role: 'admin' });
    user = await UserFactory.create();
  });

  describe('GET /api/v1/job_levels', () => {
    beforeEach(async () => {
      await JobLevelFactory.createMany(3);
    });

    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await api.get('/api/v1/job_levels');

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await api.as(user).get('/api/v1/job_levels');

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });

      it('should return 200 for admin users', async () => {
        const response = await api.as(admin).get('/api/v1/job_levels');

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(Array.isArray(response.body.data)).toBe(true);
      });
    });

    describe('successful responses', () => {
      it('should return all job levels with correct format', async () => {
        const response = await api.as(admin).get('/api/v1/job_levels');

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('pagination');
        expect(Array.isArray(response.body.data)).toBe(true);
        expect(response.body.data).toHaveLength(3);

        // Check the structure of job level objects
        const jobLevel = response.body.data[0];
        expect(jobLevel).toHaveProperty('id');
        expect(jobLevel).toHaveProperty('name');
        expect(jobLevel).toHaveProperty('order_level');
        expect(typeof jobLevel.id).toBe('number');
        expect(typeof jobLevel.name).toBe('string');
        expect(typeof jobLevel.order_level).toBe('number');
      });

      it('should include pagination information', async () => {
        const response = await api.as(admin).get('/api/v1/job_levels');

        expect(response.body.pagination).toHaveProperty('page');
        expect(response.body.pagination).toHaveProperty('limit');
        expect(response.body.pagination).toHaveProperty('total');
        expect(response.body.pagination.total).toBe(3);
      });
    });

    describe('pagination and filtering', () => {
      it('should support pagination', async () => {
        const params = { page: 1, limit: 2 };
        const response = await api.as(admin).get('/api/v1/job_levels', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(2);
        expect(response.body.pagination.page).toBe(1);
        expect(response.body.pagination.limit).toBe(2);
        expect(response.body.pagination.total).toBe(3);
      });

      it('should support search filtering', async () => {
        // Clear existing job levels and create specific ones
        await JobLevel.destroy({ where: {} });
        await JobLevel.create({ name: 'Entry Level', order_level: 1 });
        await JobLevel.create({ name: 'Senior Level', order_level: 2 });
        await JobLevel.create({ name: 'Director Level', order_level: 3 });

        const params = { search: 'Senior' };
        const response = await api.as(admin).get('/api/v1/job_levels', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].name).toBe('Senior Level');
      });

      it('should support sorting by order_level', async () => {
        const levels = await JobLevel.findAll({ order: [['id', 'ASC']], limit: 3 });
        await levels[0].update({ name: 'Entry Level', order_level: 1 });
        await levels[1].update({ name: 'Mid Level', order_level: 3 });
        await levels[2].update({ name: 'Senior Level', order_level: 2 });

        const params = { sort: 'order_level', sort_direction: 'asc' };
        const response = await api.as(admin).get('/api/v1/job_levels', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(3);
        expect(response.body.data[0].name).toBe('Entry Level');
        expect(response.body.data[1].name).toBe('Senior Level');
        expect(response.body.data[2].name).toBe('Mid Level');
      });

      it('should support sorting by order_level descending', async () => {
        const levels = await JobLevel.findAll({ order: [['id', 'ASC']], limit: 3 });
        await levels[0].update({ name: 'Entry Level', order_level: 1 });
        await levels[1].update({ name: 'Mid Level', order_level: 3 });
        await levels[2].update({ name: 'Senior Level', order_level: 2 });

        const params = { sort: 'order_level', sort_direction: 'desc' };
        const response = await api.as(admin).get('/api/v1/job_levels', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(3);
        expect(response.body.data[0].name).toBe('Mid Level');
        expect(response.body.data[1].name).toBe('Senior Level');
        expect(response.body.data[2].name).toBe('Entry Level');
      });
    });

    describe('empty results', () => {
      it('should return empty array when no job levels exist', async () => {
        await JobLevel.destroy({ where: {}, truncate: true, cascade: true });
        const response = await api.as(admin).get('/api/v1/job_levels');

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
        expect(response.body.pagination.total).toBe(0);
      });

      it('should return empty array when filter matches nothing', async () => {
        const params = { search: 'NonexistentLevel' };
        const response = await api.as(admin).get('/api/v1/job_levels', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
      });
    });

    describe('validation', () => {
      it('should handle invalid sort parameter', async () => {
        const params = { sort: 'invalid_field' };
        const response = await api.as(admin).get('/api/v1/job_levels', params);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should handle invalid sort_direction parameter', async () => {
        const params = { sort: 'order_level', sort_direction: 'invalid' };
        const response = await api.as(admin).get('/api/v1/job_levels', params);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should handle invalid page parameter', async () => {
        const params = { page: 0 };
        const response = await api.as(admin).get('/api/v1/job_levels', params);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should handle invalid limit parameter', async () => {
        const params = { limit: 0 };
        const response = await api.as(admin).get('/api/v1/job_levels', params);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should handle limit parameter exceeding maximum', async () => {
        const params = { limit: 101 };
        const response = await api.as(admin).get('/api/v1/job_levels', params);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });
    });

    describe('edge cases', () => {
      it('should handle large page numbers gracefully', async () => {
        const params = { page: 999 };
        const response = await api.as(admin).get('/api/v1/job_levels', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
        expect(response.body.pagination.page).toBe(999);
      });

      it('should handle special characters in search', async () => {
        const params = { search: 'Level@#$%' };
        const response = await api.as(admin).get('/api/v1/job_levels', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
      });
    });
  });
});
