const api = require('../utils/requestHelper');

const { UserJobVacancy } = require('../../src/models');
const {
  UserFactory,
  UserProfileFactory,
  JobTitleFactory,
  JobVacancyFactory,
  UserJobVacancyFactory,
} = require('../factories');

describe('UserJobVacanciesController', () => {
  let admin;
  let user;
  let jobTitle;
  let jobVacancy1;
  let jobVacancy2;

  beforeEach(async () => {
    admin = await UserFactory.create({ role: 'admin' });
    user = await UserFactory.create();

    // Create job title and vacancies for testing
    jobTitle = await JobTitleFactory.create();
    jobVacancy1 = await JobVacancyFactory.create({ job_title_id: jobTitle.id });
    jobVacancy2 = await JobVacancyFactory.create({ job_title_id: jobTitle.id });
  });

  describe('GET /api/v1/user_job_vacancies', () => {
    beforeEach(async () => {
      // Create user profile for the test user
      await UserProfileFactory.create({
        user_id: user.id,
        current_position: {
          role_name: 'Software Engineer',
          department: 'Engineering',
          job_grade: 'L5',
        },
      });

      // Create some test recommendations
      await UserJobVacancyFactory.create({
        user_id: user.id,
        job_vacancy_id: jobVacancy1.id,
        competency_match: 0.85,
        skill_match: 0.9,
        status: 'matched',
      });

      await UserJobVacancyFactory.create({
        user_id: user.id,
        job_vacancy_id: jobVacancy2.id,
        competency_match: 0.3,
        skill_match: 0.4,
        status: 'not_matched',
      });
    });

    it('should return 401 if not authenticated', async () => {
      const response = await api.get('/api/v1/user_job_vacancies');
      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access token required');
    });

    it('should return 403 if authenticated as a non-admin user', async () => {
      const response = await api.as(user).get('/api/v1/user_job_vacancies');
      expect(response.status).toBe(403);
      expect(response.body.error).toBe('Insufficient permissions');
    });

    it('should return all recommendations for admin without filters', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies', {
        job_vacancy_id: jobVacancy1.id,
      });

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.pagination).toBeDefined();

      // Check response structure
      const recommendation = response.body.data[0];
      expect(recommendation).toHaveProperty('id');
      expect(recommendation).toHaveProperty('user');
      expect(recommendation.user).toHaveProperty('id');
      expect(recommendation.user).toHaveProperty('name');
      expect(recommendation).toHaveProperty('status');
    });

    it('should filter recommendations by job_vacancy_id', async () => {
      const response = await api
        .as(admin)
        .get(`/api/v1/user_job_vacancies?job_vacancy_id=${jobVacancy1.id}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);

      const recommendation = response.body.data[0];
      expect(recommendation.status).toBe('matched');
    });

    it('should return empty array if job_vacancy_id has no recommendations', async () => {
      const newJobVacancy = await JobVacancyFactory.create({ job_title_id: jobTitle.id });
      const response = await api
        .as(admin)
        .get(`/api/v1/user_job_vacancies?job_vacancy_id=${newJobVacancy.id}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(0);
    });

    it('should return 400 for invalid job_vacancy_id', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies', {
        job_vacancy_id: 'invalid',
      });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Validation failed');
    });

    it('should filter by status', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies', {
        job_vacancy_id: jobVacancy1.id,
        status: 'matched',
      });

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].status).toBe('matched');
    });

    it('should support pagination', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies', {
        job_vacancy_id: jobVacancy1.id,
        page: 1,
        limit: 1,
      });

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(1);
      expect(response.body.pagination.total).toBe(1);
    });
  });

  describe('GET /api/v1/user_job_vacancies/:id', () => {
    let userJobVacancy;

    beforeEach(async () => {
      await UserProfileFactory.create({
        user_id: user.id,
        current_position: {
          role_name: 'Software Engineer',
          department: 'Engineering',
          job_grade: 'L5',
        },
      });

      userJobVacancy = await UserJobVacancyFactory.create({
        user_id: user.id,
        job_vacancy_id: jobVacancy1.id,
        status: 'matched',
      });
    });

    it('should return 401 if not authenticated', async () => {
      const response = await api.get(`/api/v1/user_job_vacancies/${userJobVacancy.id}`);
      expect(response.status).toBe(401);
    });

    it('should return 403 if authenticated as a non-admin user', async () => {
      const response = await api.as(user).get(`/api/v1/user_job_vacancies/${userJobVacancy.id}`);
      expect(response.status).toBe(403);
    });

    it('should return specific recommendation for admin', async () => {
      const response = await api.as(admin).get(`/api/v1/user_job_vacancies/${userJobVacancy.id}`);

      expect(response.status).toBe(200);
      expect(response.body.data.id).toBe(userJobVacancy.id);
      expect(response.body.data.status).toBe('matched');
      expect(response.body.data.user.id).toBe(user.id);
    });

    it('should return 404 for non-existent recommendation', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies/99999');

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('User job vacancy recommendation not found');
    });

    it('should handle invalid ID parameter', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies/invalid');

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('additional edge cases', () => {
    it('should handle empty results when no recommendations exist', async () => {
      // Clear all existing recommendations
      await UserJobVacancy.destroy({ where: {} });

      const response = await api.as(admin).get('/api/v1/user_job_vacancies', {
        job_vacancy_id: jobVacancy1.id,
      });

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual([]);
      expect(response.body.pagination.total).toBe(0);
    });

    it('should return 404 when filtering by non-existent job_vacancy_id', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies', {
        job_vacancy_id: 99999,
      });

      expect(response.status).toBe(404);
    });

    it('should handle invalid job_vacancy_id parameter', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies', {
        job_vacancy_id: 'invalid',
      });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Validation failed');
    });

    it('should handle large page numbers gracefully', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies', {
        job_vacancy_id: jobVacancy1.id,
        page: 999,
      });

      expect(response.status).toBe(200);
      expect(response.body.data).toEqual([]);
      expect(response.body.pagination.page).toBe(999);
    });

    it('should handle invalid pagination parameters', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies', {
        job_vacancy_id: jobVacancy1.id,
        page: 0,
        limit: 0,
      });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Validation failed');
    });

    it('should handle limit exceeding maximum', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies', {
        job_vacancy_id: jobVacancy1.id,
        limit: 101,
      });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Validation failed');
    });

    it('should handle invalid sort parameters', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies', {
        job_vacancy_id: jobVacancy1.id,
        sort: 'invalid_field',
      });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Validation failed');
    });

    it('should handle invalid sort_direction parameter', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies', {
        job_vacancy_id: jobVacancy1.id,
        sort: 'created_at',
        sort_direction: 'invalid',
      });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Validation failed');
    });

    it('should return recommendations with all required fields', async () => {
      // Create test data for this specific test
      const testUser = await UserFactory.create();
      const testJobVacancy = await JobVacancyFactory.create({ job_title_id: jobTitle.id });
      await UserJobVacancyFactory.create({
        user_id: testUser.id,
        job_vacancy_id: testJobVacancy.id,
        competency_match: 0.75,
        skill_match: 0.8,
        status: 'matched',
      });

      const response = await api.as(admin).get('/api/v1/user_job_vacancies', {
        job_vacancy_id: testJobVacancy.id,
        limit: 1,
      });

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);

      const recommendation = response.body.data[0];
      expect(recommendation).toHaveProperty('id');
      expect(recommendation).toHaveProperty('status');
      expect(recommendation).toHaveProperty('user');
      expect(recommendation.user).toHaveProperty('id');
      expect(recommendation.user).toHaveProperty('name');
    });

    it('should handle sorting by different fields', async () => {
      // Create test data for this specific test
      const testUser = await UserFactory.create();
      const testJobVacancy = await JobVacancyFactory.create({ job_title_id: jobTitle.id });
      await UserJobVacancyFactory.create({
        user_id: testUser.id,
        job_vacancy_id: testJobVacancy.id,
        competency_match: 0.75,
        skill_match: 0.8,
        status: 'matched',
      });

      const sortFields = ['created_at', 'updated_at'];

      for (const sortField of sortFields) {
        const response = await api.as(admin).get('/api/v1/user_job_vacancies', {
          job_vacancy_id: testJobVacancy.id,
          sort: sortField,
          sort_direction: 'desc',
        });

        expect(response.status).toBe(200);
        expect(response.body.data.length).toBeGreaterThan(0);
      }
    });
  });
});
