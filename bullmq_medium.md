## Part 1 of a 3-Part Series on Building a Robust Message Queue with BullMQ!

# How We Built a Robust Message Queue Using BullMQ - Part-1

[![Karthik S](https://miro.medium.com/v2/da:true/resize:fill:64:64/1*NpbCWQlpqwD_OWHaN2T5VA.gif)](https://medium.com/@karthiks05?source=post_page---byline--2d5ad1016958---------------------------------------)

[Karthik S](https://medium.com/@karthiks05?source=post_page---byline--2d5ad1016958---------------------------------------)

11 min read

·

Oct 10, 2024

[nameless link](https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fvote%2Fp%2F2d5ad1016958&operation=register&redirect=https%3A%2F%2Fmedium.com%2F%40karthiks05%2Fhow-we-built-a-robust-message-queue-using-bullmq-part-1-2d5ad1016958&user=Karthik+S&userId=c192422fc85f&source=---header_actions--2d5ad1016958---------------------clap_footer------------------)

--

3

[nameless link](https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2F_%2Fbookmark%2Fp%2F2d5ad1016958&operation=register&redirect=https%3A%2F%2Fmedium.com%2F%40karthiks05%2Fhow-we-built-a-robust-message-queue-using-bullmq-part-1-2d5ad1016958&source=---header_actions--2d5ad1016958---------------------bookmark_footer------------------)

Listen

Share

![captionless image](https://miro.medium.com/v2/resize:fit:1400/format:webp/1*Z7b8SU1PsWTPt1jPEvizAg.jpeg)

## About Me

Hey, I’m Karthik!

Despite having a background in computer science, my journey into tech started
off on an unconventional path. I began my career with almost no hands-on
experience and, honestly, very little interest in my subjects.

Fast forward to today, and I’m proud to call myself a software engineer who’s
passionate about solving real-world problems.

But my journey isn’t just about building software—it’s about helping others who
feel stuck, just like I once did. I believe tech is more about how you solve
problems than the technical details you know. I’m here to share what I’ve
learned and hopefully guide others through their own tech journeys.

## Prerequisite

This guide assumes you have knowledge and hands-on experience with Node.js,
Express, Redis, and BullMQ. Don’t worry if you’re not familiar with any of these
technologies—I've got you covered. Here are some official resources to help you
get started:

- [Node.js](https://nodejs.org/en)
- [Nest.js](https://nestjs.com/)
- [Express.js](https://expressjs.com/)
- [Redis](https://redis.io/)
- [BullMQ](https://docs.bullmq.io/)

Take a moment to explore these resources if needed. Otherwise, let’s dive in!

1. Overview

---

In this blog, I’ll walk you through how we tackled a key challenge by
implementing a robust message queue using BullMQ in our NestJS application. From
the initial feature request to designing and deploying a scalable solution,
you’ll get an inside look at the entire journey.

What’s inside:

- The challenge we faced and how it shaped our approach
- The reasoning behind choosing the right communication pattern
- Why BullMQ was the perfect fit for our needs and how we plugged it in
- A hands-on guide to building a producer-consumer architecture from scratch

Whether you’re an experienced developer or just getting started, this post will
give you practical insights into building efficient, scalable systems using the
right tools.

2. Requirements and Constraints

---

Before we get into the nuts and bolts of the implementation, let’s break down
the key requirements and constraints that our stakeholders have outlined for the
vendor performance evaluation system:

## 2.1 Key Requirements:

- **Vendor Evaluation:** Design a robust system that can assess vendor
  performance as the vendor base expands.
- **Data Points:** Evaluate each vendor based on **eight specific parameters**,
  such as payment history, customer feedback, and ratings, among others.
- **Scoring System:** Establish a performance scoring system that rates each
  vendor on a scale from **1 to 5**.
- **Configurable Parameters:** Provide users with the ability to modify
  parameter values directly within the system for better customization.

### 2.2 Constraints:

- **On-Demand Calculation**: Manual selection and calculation for specific
  vendors
- **Scheduled Calculation**: Automatic daily calculations using Cron Jobs

These requirements and constraints highlight the **need for a robust, scalable
system capable of handling large datasets** and providing flexible calculation
options.

To meet these needs, we carefully evaluated different communication patterns to
determine the best fit for our use case.

This evaluation _included comparing request-response, pub-sub, and
producer-consumer models_. By thoroughly assessing each option, we ensured that
we chose an approach that aligned with our client’s needs while maintaining
system integrity.

3. Use Case: Choosing the Right Communication Pattern

---

To meet the client’s requirements for vendor performance evaluation, we
evaluated three common communication patterns: Request-Response, Pub-Sub, and
Producer-Consumer. Here’s a comparison to determine the best fit for this use
case:

![captionless image](https://miro.medium.com/v2/resize:fit:1400/format:webp/1*EdYLmTToMheKLCussQnLIQ.jpeg)

### 3.1 Analysis:

- **Request-Response**: While simple, this pattern doesn’t suit the requirement
  of deferred results and would struggle with large data sets.
- **Pub-Sub**: This pattern can handle broadcasting calculation requests but
  isn’t ideal for managing specific tasks that require deferred processing for
  each user.
- **Producer-Consumer**: This pattern aligns perfectly with the client’s needs.
  It supports asynchronous processing, allowing calculations to be queued and
  processed over time, thus fitting the requirement for deferred results while
  efficiently handling large volumes of data.

### 3.2 Conclusion:

Given the requirements for on-demand and scheduled processing without immediate
response, the **producer-consumer** pattern emerges as the optimal solution for
this use case.

_It offers scalability, fault tolerance, and the capacity to handle large data
sets asynchronously — making it the perfect fit for our client’s needs._

![captionless image](https://miro.medium.com/v2/resize:fit:1400/format:webp/1*IFulCwuPaywvUD78kyRXPw.gif)

Before tackling the challenges, **it’s crucial to start with a high-level
architecture overview.**

Sketch out a rough design or use online tools to visualize the architecture.
This process helps identify potential flaws, bottlenecks, or necessary changes
in the existing system.

Let’s list down all the challenges we faced.

4. Challenges

---

![captionless image](https://miro.medium.com/v2/resize:fit:1400/format:webp/1*GEMpYy_J_yBUNuGGSWdIOA.jpeg)

1.  **Introducing a Producer-Consumer:** Having chosen the producer-consumer
    model as our communication strategy, we needed to implement this feature
    within our system.
2.  **Maintaining Existing Architecture:** We aimed to minimize significant
    architectural changes to the existing application (a topic we’ll explore in
    depth later). For now, our focus is on implementing the producer-consumer
    model.
3.  **Potential Server Overload:** This challenge is linked to the first two. As
    we introduce the producer-consumer feature, there’s a risk of server
    overload. If this happens, we may need to reconsider the architecture that
    we aim to avoid. I’ll delve into this more in future posts.
4.  **Service Coupling:** To prevent overwhelming the application, it’s
    essential to decouple services as much as possible. By decoupling, each
    service operates independently, making it easier to scale specific services
    without affecting others. This approach not only avoids bottlenecks but also
    enhances system flexibility and maintainability. If services are tightly
    coupled, scaling becomes complex and can lead to inefficiencies, directly
    impacting the application’s performance and resilience.
5.  **Handling Message Queues** Managing message queues is crucial, especially
    if the producer generates more jobs or messages. Conversely, the customer
    needs to manage this well (for the time being, we will handle this at the
    application level).

Did you notice how we deconstructed the feature into core components? This
approach helped us identify necessary adjustments for seamless integration.

_To succeed as a software engineer, recognize potential challenges before
tackling a new feature. Understanding the problem fully before crafting a
solution enhances your problem-solving skills._

I encourage you to adopt this method in your work. If you found this helpful,
feel free to like, comment, or share it with your colleagues.

Ready to explore the solutions? Let’s jump in!

5. Solution

---

As software engineers, we often say, _“At the end of the day, we must deliver
results, regardless of the challenges we face.”_

![captionless image](https://miro.medium.com/v2/resize:fit:996/format:webp/1*RkBHPBZgvtZ3bUkMXd1pBw.gif)

Let’s break down the challenges step by step.

By focusing on each challenge individually, we can allocate our efforts more
effectively and avoid being overwhelmed. This method, known as **“Divide and
Conquer,”** simplifies the problem-solving process and often leads to better
solutions.

**Challenge 1**: Implementing a Producer-Consumer Architecture

**Solution**: After thorough research, _BullMQ_ emerged as the best fit for our
task processing needs.

**Challenges 2, 3, & 4**: Architectural Considerations, Coupling, and Server
Overload

**Solution:** I’ll cover these in upcoming blog posts, where we’ll explore how
we tackled these issues.

**Challenge 5**: Handling High Volumes of Jobs and Messages

**Solution:** We managed this by adjusting BullMQ’s concurrency settings. If
you’re new to concurrency,
[here’s a useful link](https://stackoverflow.com/questions/1050222/what-is-the-difference-between-concurrency-and-parallelism).

```
import { Worker, Job } from 'bullmq';
const worker = new Worker(
  queueName,
  async (job: Job) => {
    // Process the job
    return 'some value';
  },
  { concurrency: 50 } // Adjust concurrency level here
);
```

Find out more about concurrency in BullMQ
[_here_](https://docs.bullmq.io/guide/workers/concurrency).

### 5.1 Reasons Why We Chose BullMQ

1.  **Seamless Integration**: Although other options like RabbitMQ are
    excellent, BullMQ fit our project requirements more precisely, offering
    smoother integration with our existing setup.
2.  **Redis-Based**: BullMQ leverages Redis, which we were already using. It
    stores all metadata, queue information, job data, and statuses in Redis,
    making it ideal for our needs.
3.  **Robust Retry Mechanism**: BullMQ supports configurable retry strategies,
    either through retry counts or fallbacks. This flexibility ensures automatic
    retries, improving error handling and system resilience.
4.  **Scalability**: BullMQ scales well with options like local concurrency and
    multiple workers. We configured local concurrency to handle large job
    volumes efficiently, ensuring smooth performance under heavy load.
5.  **Dashboard & Monitoring**: The
    [_Bull-Board_](https://www.npmjs.com/package/@bull-board/ui) UI offers:

- A clear view of all queues.
- Job statuses (success, failure, stale, etc.).
- Error logs and the option to re-run failed jobs directly from the UI.

Great, we’ve addressed solutions for two of our challenges. But are we done? Not
quite yet. Identifying solutions is just the beginning — now it’s time to
implement them.

Finally, the moment we’ve all been waiting for! Grab a coffee, and let’s dive
into the implementation and start writing some code!

![captionless image](https://miro.medium.com/v2/resize:fit:1200/format:webp/1*pzt9dWXASsJj2Ep-ZG2KWQ.gif)

6. Implementation

---

We followed the _“Keep It Simple & Stupid” (KISS)_ principle to streamline the
process.

Here’s how we built a robust producer-consumer architecture: **NestJS + BullMQ +
Redis + BullBoard**

### 6.1 Install the Required Packages

First, install the required dependencies

```
npm install @nestjs/bullmq bullmq redis @bull-board/nestjs @nestjs/config
```

### 6.2 Create `.env` File

Make sure you create a `.env` file to define the environment variables needed
for Redis:

```
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=yourpassword  # Leave it blank if no password
```

### 6.3 Queue Module Configuration (Redis + BUllMQ+ BullBoard)

This module handles the configuration for BullMQ, Redis, and BullBoard.

```
// queue.module.ts
import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ExpressAdapter } from '@bull-board/express';
import { QueueProducerService } from './queue-producer.service';
import { QueueConsumerService } from './queue-consumer.service';
import { EnvVariable } from './env.variables'; // Create or define EnvVariable constants
@Module({
  imports: [    ConfigModule.forRoot({ cache: true, isGlobal: true }),
    BullModule.forRootAsync({
      useFactory: async (configService: ConfigService) => ({
        connection: {
          host: configService.get(EnvVariable.REDIS_HOST),
          port: configService.get(EnvVariable.REDIS_PORT),
          password: configService.get(EnvVariable.REDIS_PASSWORD),
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue(...QueueList),
    BullBoardModule.forRoot({ route: '/queues', adapter: ExpressAdapter }),
    BullBoardModule.forFeature(...QueueListBoard),
  ],
  providers: [QueueProducerService, QueueConsumerService],
  exports: [QueueProducerService],
})
export class QueueModule {}
```

### 6.4 Create Producer Service

This service is responsible for adding jobs to the queue. It uses the `BullMQ`
queue to enqueue tasks.

```
// queue-producer.service.ts
import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
@Injectable()
export class QueueProducerService {
  constructor(@InjectQueue('exampleQueue') private readonly queue: Queue) {}
  async addJob(data: any) {
    await this.queue.add('exampleJob', data, {
      attempts: 3,    // Retry up to 3 times
      backoff: 5000,  // Wait 5 seconds between retries
    });
    console.log('Job added to queue');
  }
}
```

### 6.5 Create Consumer Service

The **Consumer** processes jobs from the queue. When jobs are added, this
service picks them up and processes them.

```
// queue-consumer.service.ts
import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
@Processor('exampleQueue') // This corresponds to the queue name
export class QueueConsumerService extends WorkerHost {
  async process(job: Job<any>): Promise<any> {
    console.log(`Processing job id ${job.id} with data:`, job.data);
    // Add your job processing logic here
    try {
      // Simulate job processing
      console.log('Job processed successfully');
    } catch (error) {
      console.error('Error processing job:', error);
      throw error; // Re-queue job in case of failure
    }
  }
}
```

### 6.6 Adding Jobs to the Queue:

Inject the `QueueProducerService` into your controller or any service to start
adding jobs:

```
import { Controller, Get } from '@nestjs/common';
import { QueueProducerService } from './queue-producer.service';
@Controller('jobs')
export class JobsController {
  constructor(private readonly producerService: QueueProducerService) {}
  @Get('add')
  async addJob() {
    const data = { name: 'BullMQ job', priority: 'high' };
    await this.producerService.addJob(data);
    return 'Job added successfully!';
  }
}
```

We had a total of **8 data points** that needed to be calculated across the
system. To manage this effectively, we decided to create **8 distinct queues**
along with their corresponding consumers.

This approach allows us to:

- **Debug Effectively**: Isolate issues within specific queues, enabling quick
  identification and resolution of data point mismatches.
- **Monitor Failed Jobs**: Easily track and manage failed jobs associated with
  each queue, ensuring prompt problem resolution.
- **Scale Efficiently**: Adjust the architecture to meet the unique demands of
  each queue, optimizing both performance and resource allocation.

![captionless image](https://miro.medium.com/v2/resize:fit:1400/format:webp/1*fmzN3mb2QIKhOxsAEIPzfg.gif)

## Recap

In this blog post, we explored how we built a robust Message Queue using BullMQ
to solve a complex vendor performance evaluation problem. Here’s a summary of
what we covered:

- Identified the need for a producer-consumer architecture to handle large-scale
  data processing
- Choose BullMQ for its Redis integration, retry mechanisms, and monitoring
  capabilities
- Implemented separate queues for different data points to improve debugging and
  scalability
- Configured BullMQ in a Nest.js environment and set up a dashboard for
  monitoring
- Discussed challenges and solutions in implementing this architecture

## Conclusion and Future Directions

By implementing BullMQ and following the strategies outlined in this post, we
successfully built a robust producer-consumer architecture that effectively
addressed our vendor performance evaluation challenges. The system not only met
our immediate needs but also provided a foundation for future growth and
optimization.

Some key takeaways from our implementation include:

- The importance of choosing the right tool for the job: BullMQ’s features
  aligned perfectly with our requirements.
- The value of breaking down complex problems into manageable components, as
  demonstrated by our queue-per-data-point approach.
- The benefits of a well-designed architecture in terms of debugging,
  monitoring, and scalability.

Looking ahead, we’re exploring several avenues for further improvement:

- Implementing more advanced monitoring and alerting systems to proactively
  identify and address potential issues.
- Exploring BullMQ’s more advanced features, such as job prioritization and
  delayed jobs, to further optimize our workflow.
- Conducting performance benchmarks to quantify the improvements and identify
  any remaining bottlenecks.

We hope this deep dive into our experience with BullMQ has been informative and
inspiring. “**_Remember, every system has room for improvement, and the journey
of optimization is ongoing._**_”_ We’d love to hear about your experiences with
message queues and any innovative solutions you’ve implemented in your projects!

By following these steps and strategies, we effectively addressed the challenges
and built an efficient solution.

## What’s Coming Next

In the next part of this series, we’ll explore how we successfully scaled our
application without the need for a microservice architecture.

Here’s what’s on the horizon:

- **Horizontal Scaling Strategies:** We’ll dive into creative approaches for
  implementing horizontal scaling within a monolithic system, ensuring our
  application can handle growth effectively and efficiently.

## Call to Action

Ready to level up your backend engineering skills? Here’s what you can do:

- Implement a small-scale producer-consumer system using BullMQ in your next
  project
- Share your experiences or challenges in the comments below
- Follow this blog for the upcoming post on scaling without microservices

**P.S.** Are you facing similar challenges in your projects? Let’s discuss in
the comments! Your insights could be the key to unlocking new solutions for the
community. Don’t forget to share this post with your team if you found it
valuable!

![captionless image](https://miro.medium.com/v2/resize:fit:598/format:webp/1*hmddD4QY_gJJzlFfGmrN2A.jpeg)

Thank you for reading! If you found this post helpful, please consider following
for more insights on software engineering, architecture, and best practices.
Your support through likes, shares, and subscriptions helps me continue creating
valuable content.

I’d love to hear your thoughts or stories too — feel free to drop a comment or
connect with me on [LinkedIn](https://www.linkedin.com/in/karthiks05). I share
**bite-sized engineering stories**, lessons from the trenches.

👉 You can find all my social handles [here](https://linktr.ee/karthiks05) — a
mix of professional insights and personal passions.

I appreciate your time and hope this article provided useful information for
your projects. “_Remember, every coding journey is unique, so keep exploring and
learning!_”

_Until next time, happy coding and stay curious!_

This is just the beginning! Don’t miss the next part of this series. Read my
second blog,
[Beyond Microservices](https://medium.com/@karthiks05/beyond-microservices-streamlining-scaling-and-decoupling-with-a-hybrid-nestjs-architecture-f3adece72949)
to continue the journey.
