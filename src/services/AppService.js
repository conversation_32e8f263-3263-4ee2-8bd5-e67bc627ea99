const config = require('../config/config');
const InvalidError = require('../errors/InvalidError');
const NotFoundError = require('../errors/NotFoundError');
const { sequelize, namespace } = require('../models');

class AppService {
  constructor() {
    this.config = config;
  }

  assert(condition, message) {
    if (!condition) {
      throw new InvalidError(message);
    }
  }

  exists(object, message) {
    if (!object) {
      throw new NotFoundError(message);
    }
  }

  async transaction(callback) {
    const transaction = await namespace.get('transaction');
    const options = transaction ? { transaction } : {};
    return await sequelize.transaction(options, callback);
  }
}

module.exports = AppService;
