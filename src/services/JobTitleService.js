const AppService = require('./AppService');
const JobTitlesRepository = require('../repositories/JobTitlesRepository');
const { JobTitle } = require('../models');

class JobTitleService extends AppService {
  constructor() {
    super();
    this.repository = new JobTitlesRepository();
  }

  /**
   * Get all job titles with pagination
   * @param {Object} params - Query params (page, limit, etc.)
   * @returns {Object} - Job titles array and pagination info
   */
  async findAll(params = {}) {
    const { rows, pagination } = await this.repository.findAll(params);
    return { job_titles: rows, pagination };
  }

  async upsertPrefilledDetails(data) {
    return this.transaction(async () => {
      if (!data.id && !data.name) {
        console.warn('No job title id or name provided, skipping upsert');
        return null;
      }

      const findQuery = data.id ? { id: data.id } : { name: data.name };
      const jobTitle = await JobTitle.findOne({
        where: findQuery,
        paranoid: false,
      });

      if (!jobTitle) {
        return JobTitle.create(data);
      }

      const existingDetails = jobTitle.prefilled_details || {};
      const newDetails = data.prefilled_details || {};

      const truthyNewDetails = Object.fromEntries(
        Object.entries(newDetails).filter(([, value]) => value),
      );

      const mergedDetails = { ...existingDetails, ...truthyNewDetails };
      return jobTitle.update({
        prefilled_details: mergedDetails,
        deleted_at: null,
      });
    });
  }
}

module.exports = JobTitleService;
