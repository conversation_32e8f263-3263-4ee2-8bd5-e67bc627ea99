'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // List of all tables except job_vacancies (which already has deleted_at)
    const tables = [
      'bones',
      'internal_job_profile_data',
      'job_group_variables',
      'job_levels',
      'job_titles',
      'job_variables',
      'llm_metadata',
      'user_assessment_results',
      'user_bones',
      'user_competencies_profilings',
      'user_job_vacancies',
      'user_job_variable_constants',
      'user_job_variables',
      'user_performance_reviews',
      'user_positions',
      'user_profiles',
      'user_vacancy_group_variables',
      'user_vacancy_variable_scores',
      'users',
      'vacancy_group_variables',
      'work_areas',
    ];

    // Add deleted_at column to all tables
    for (const table of tables) {
      await queryInterface.addColumn(table, 'deleted_at', {
        type: Sequelize.DATE,
        allowNull: true,
      });
    }
  },

  async down(queryInterface, _Sequelize) {
    // List of all tables except job_vacancies (which should keep deleted_at)
    const tables = [
      'bones',
      'internal_job_profile_data',
      'job_group_variables',
      'job_levels',
      'job_titles',
      'job_variables',
      'llm_metadata',
      'user_assessment_results',
      'user_bones',
      'user_competencies_profilings',
      'user_job_vacancies',
      'user_job_variable_constants',
      'user_job_variables',
      'user_performance_reviews',
      'user_positions',
      'user_profiles',
      'user_vacancy_group_variables',
      'user_vacancy_variable_scores',
      'users',
      'vacancy_group_variables',
      'work_areas',
    ];

    // Remove deleted_at column from all tables
    for (const table of tables) {
      await queryInterface.removeColumn(table, 'deleted_at');
    }
  },
};
