const { Sequelize } = require('sequelize');
const cls = require('cls-hooked');
const config = require('./database.json');

const namespace = cls.createNamespace('paragon-app');
Sequelize.useCLS(namespace);

const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

const sequelize = new Sequelize(dbConfig.database, dbConfig.username, dbConfig.password, {
  host: dbConfig.host,
  port: dbConfig.port,
  dialect: dbConfig.dialect,
  logging: dbConfig.logging,
  pool: dbConfig.pool,
  dialectOptions: dbConfig.dialectOptions || {},
});

module.exports = { Sequelize, sequelize, dbConfig, namespace };
