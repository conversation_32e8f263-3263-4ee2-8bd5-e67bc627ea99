const ApiOutput = require('./ApiOutput');

class JobVacancyOutput extends ApiOutput {
  /**
   * Format a single job vacancy for output
   * @param {Object} item - Job vacancy item to format (optional, uses this.data if not provided)
   * @returns {Object} Formatted job vacancy data
   */
  format() {
    return {
      id: this.data.id,
      name: this.data.name,
      job_desc: this.data.job_desc,
      job_description: this.data.job_description,
      detailed_descriptions: this.data.detailed_descriptions,
      reference_users: this.referenceUsersOutput(),
      status: this.data.status,
      job_level: this.jobLevelOutput(),
      bone: this.boneOutput(),
      work_area: this.workAreaOutput(),
      relevant_working_experience: this.data.relevant_working_experience,
      role_summary: this.data.role_summary,
      job_title: this.jobTitleOutput(),
    };
  }

  listFormat() {
    return {
      id: this.data.id,
      name: this.data.name,
      status: this.data.status,
      created_at: this.data.created_at,
      ujv_count: this.options.countsById[this.data.id],
      job_level: this.jobLevelOutput(),
      bone: this.boneOutput(),
      work_area: this.workAreaOutput(),
      relevant_working_experience: this.data.relevant_working_experience,
      role_summary: this.data.role_summary,
      job_title: this.jobTitleOutput(),
    };
  }

  referenceUsersOutput() {
    if (!this.options.referenceUsers) return [];

    return this.options.referenceUsers.map(user => {
      return {
        id: user.id,
        name: user.name,
      };
    });
  }

  jobLevelOutput() {
    if (!this.data.jobLevel) return {};

    return {
      id: this.data.jobLevel.id,
      name: this.data.jobLevel.name,
      order_level: this.data.jobLevel.order_level,
    };
  }

  boneOutput() {
    if (!this.data.bone) return {};

    return {
      id: this.data.bone.id,
      name: this.data.bone.name,
    };
  }

  workAreaOutput() {
    if (!this.data.workArea) return {};

    return {
      id: this.data.workArea.id,
      name: this.data.workArea.name,
    };
  }

  jobTitleOutput() {
    if (!this.data.jobTitle) return {};

    return {
      id: this.data.jobTitle.id,
      name: this.data.jobTitle.name,
    };
  }
}

module.exports = JobVacancyOutput;
